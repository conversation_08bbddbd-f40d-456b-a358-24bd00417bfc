
import React from 'react';
import { Participant } from '../types';

interface ParticipantIconProps {
  participant: Participant;
  size?: 'sm' | 'md' | 'lg';
}

export const ParticipantIcon: React.FC<ParticipantIconProps> = ({ participant, size = 'md' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-16 h-16',
  };

  return (
    <img
      src={participant.avatarUrl}
      alt={participant.name}
      title={participant.name}
      className={`${sizeClasses[size]} rounded-full object-cover ring-2 ring-brand-surface-light`}
    />
  );
};
