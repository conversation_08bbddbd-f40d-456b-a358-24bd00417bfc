
import React from 'react';
import { Debate, VoteSide, Reference } from '../types';
import { ParticipantIcon } from './ParticipantIcon';
import { ArgumentCard } from './ArgumentCard';
import { LiveDebateStage } from './LiveDebateStage';
import { ArrowLeftIcon, LinkIcon } from './icons';

interface DebateViewProps {
  debate: Debate;
  onBack: () => void;
  onVote: (side: VoteSide, argumentId?: string) => void;
}

const ReferenceList: React.FC<{references: Reference[]}> = ({ references }) => (
  <div>
    <h4 className="text-sm font-semibold text-gray-400 uppercase tracking-wider mb-2">Evidence</h4>
    <ul className="space-y-1">
      {references.map(ref => (
        <li key={ref.id}>
          <a href={ref.url} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 text-brand-secondary/80 hover:text-brand-secondary text-sm transition-colors">
            <LinkIcon className="w-4 h-4" />
            <span>{ref.title}</span>
          </a>
        </li>
      ))}
    </ul>
  </div>
);

export const DebateView: React.FC<DebateViewProps> = ({ debate, onBack, onVote }) => {
  const totalVotes = debate.votesFor + debate.votesAgainst;
  const forPercentage = totalVotes > 0 ? (debate.votesFor / totalVotes) * 100 : 50;
  
  const forArguments = debate.arguments.filter(a => a.side === VoteSide.FOR);
  const againstArguments = debate.arguments.filter(a => a.side === VoteSide.AGAINST);

  return (
    <div className="max-w-6xl mx-auto p-4 md:p-8">
      <button onClick={onBack} className="flex items-center gap-2 text-brand-text-light hover:text-white mb-8 transition-colors">
        <ArrowLeftIcon className="w-5 h-5"/>
        Back to All Debates
      </button>

      {/* Main Claim */}
      <div className="bg-brand-surface rounded-xl shadow-lg p-6 mb-8">
        <h1 className="text-3xl font-extrabold text-white mb-2">{debate.title}</h1>
        <p className="text-brand-text-light mb-6">{debate.description}</p>
        <div className="bg-brand-bg/50 rounded-lg p-6">
            <p className="text-lg text-brand-text mb-4 italic">"{debate.claim.text}"</p>
            <div className="flex justify-between items-end">
                {debate.claim.references.length > 0 && <ReferenceList references={debate.claim.references} />}
                <div className="flex flex-col items-end">
                    <ParticipantIcon participant={debate.claim.author} />
                    <span className="text-sm font-semibold mt-2 text-brand-text-light">{debate.claim.author.name}</span>
                </div>
            </div>
        </div>
      </div>
      
      {/* Voting Section */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-center mb-4">Overall Vote</h2>
        <div className="flex items-center gap-4 mb-2">
            <button onClick={() => onVote(VoteSide.FOR)} className="bg-success/80 hover:bg-success text-white font-bold py-2 px-4 rounded-lg transition-colors w-32 text-center">
                FOR ({debate.votesFor})
            </button>
            <div className="w-full bg-brand-surface rounded-full h-6">
                <div className="bg-success h-6 rounded-l-full" style={{ width: `${forPercentage}%` }}></div>
                <div className="bg-danger h-6 rounded-r-full" style={{ width: `${100 - forPercentage}%`, marginLeft: `${forPercentage}%`, marginTop: '-24px' }}></div>
            </div>
            <button onClick={() => onVote(VoteSide.AGAINST)} className="bg-danger/80 hover:bg-danger text-white font-bold py-2 px-4 rounded-lg transition-colors w-32 text-center">
                AGAINST ({debate.votesAgainst})
            </button>
        </div>
      </div>

      {/* Live Stage */}
      {debate.isLive && (
        <div className="mb-12">
          <LiveDebateStage proponent={debate.proponent} opponent={debate.opponent}/>
        </div>
      )}

      {/* Arguments Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h3 className="text-2xl font-bold mb-4 border-l-4 border-success pl-3 text-success">Arguments For</h3>
          <div className="space-y-4">
            {forArguments.length > 0 ? (
                forArguments.map(arg => <ArgumentCard key={arg.id} argument={arg} onVote={() => onVote(VoteSide.FOR, arg.id)} />)
            ) : <p className="text-brand-text-light italic">No arguments for this side yet.</p>}
          </div>
        </div>
        <div>
          <h3 className="text-2xl font-bold mb-4 border-l-4 border-danger pl-3 text-danger">Arguments Against</h3>
          <div className="space-y-4">
            {againstArguments.length > 0 ? (
                againstArguments.map(arg => <ArgumentCard key={arg.id} argument={arg} onVote={() => onVote(VoteSide.AGAINST, arg.id)} />)
            ) : <p className="text-brand-text-light italic">No arguments for this side yet.</p>}
          </div>
        </div>
      </div>
    </div>
  );
};
