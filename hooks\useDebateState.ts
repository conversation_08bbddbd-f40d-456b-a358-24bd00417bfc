
import React, { useState, useCallback } from 'react';
import { Debate, VoteSide } from '../types';

const MOCK_PARTICIPANTS = {
  userA: { id: 'u1', name: '<PERSON>', avatarUrl: 'https://picsum.photos/seed/u1/100' },
  userB: { id: 'u2', name: '<PERSON>', avatarUrl: 'https://picsum.photos/seed/u2/100' },
  userC: { id: 'u3', name: '<PERSON>', avatarUrl: 'https://picsum.photos/seed/u3/100' },
  userD: { id: 'u4', name: '<PERSON><PERSON>', avatarUrl: 'https://picsum.photos/seed/u4/100' },
};

const MOCK_DEBATES: Debate[] = [
  {
    id: 'd1',
    title: 'Is artificial intelligence a net positive for humanity?',
    description: 'A deep-dive into the societal, economic, and ethical implications of advancing AI technology.',
    claim: {
      author: MOCK_PARTICIPANTS.userA,
      text: 'The continued development and integration of artificial intelligence will ultimately yield more benefits than harms, driving unprecedented progress in science, medicine, and human efficiency.',
      references: [{id: 'r1', title: 'Global AI Development Index 2023', url: '#' }],
    },
    proponent: MOCK_PARTICIPANTS.userA,
    opponent: MOCK_PARTICIPANTS.userB,
    participants: [MOCK_PARTICIPANTS.userA, MOCK_PARTICIPANTS.userB, MOCK_PARTICIPANTS.userC],
    arguments: [
      { id: 'a1', author: MOCK_PARTICIPANTS.userA, text: 'AI is accelerating drug discovery and helping to diagnose diseases earlier and more accurately than ever before.', side: VoteSide.FOR, votes: 128, references: [] },
      { id: 'a2', author: MOCK_PARTICIPANTS.userB, text: 'Unchecked AI development risks massive job displacement and could exacerbate economic inequality on a global scale.', side: VoteSide.AGAINST, votes: 95, references: [{id: 'r2', title: 'Economic Forum Report on Future of Jobs', url: '#' }] },
      { id: 'a3', author: MOCK_PARTICIPANTS.userC, text: 'The use of AI in creative fields is not replacing artists but providing them with powerful new tools for expression.', side: VoteSide.FOR, votes: 42, references: [{id: 'r3', title: 'Art & Machina: A Survey', url: '#' }] },
    ],
    votesFor: 170,
    votesAgainst: 95,
    isLive: true,
  },
  {
    id: 'd2',
    title: 'Should space exploration be privatized?',
    description: 'Examining the pros and cons of shifting the primary responsibility for space exploration from government agencies to private corporations.',
    claim: {
      author: MOCK_PARTICIPANTS.userD,
      text: 'The privatization of space exploration accelerates innovation, reduces costs, and opens up the cosmos for humanity in a way that public funding alone cannot.',
      references: [],
    },
    proponent: MOCK_PARTICIPANTS.userD,
    opponent: MOCK_PARTICIPANTS.userC,
    participants: [MOCK_PARTICIPANTS.userD, MOCK_PARTICIPANTS.userC, MOCK_PARTICIPANTS.userA],
    arguments: [
      { id: 'b1', author: MOCK_PARTICIPANTS.userC, text: 'Relying on private companies introduces a profit motive that could compromise scientific objectives and safety standards.', side: VoteSide.AGAINST, votes: 78, references: [] },
      { id: 'b2', author: MOCK_PARTICIPANTS.userA, text: 'Private competition has already driven down launch costs significantly, making space more accessible.', side: VoteSide.FOR, votes: 150, references: [{id: 'r4', title: 'Analysis of Launch Costs (2010-2024)', url: '#' }] },
    ],
    votesFor: 150,
    votesAgainst: 78,
    isLive: false,
  },
];


export const useDebateState = () => {
  const [debates, setDebates] = useState<Debate[]>(MOCK_DEBATES);
  const [selectedDebateId, setSelectedDebateId] = useState<string | null>(null);

  const selectDebate = useCallback((id: string | null) => {
    setSelectedDebateId(id);
  }, []);

  const castVote = useCallback((debateId: string, side: VoteSide, argumentId?: string) => {
    setDebates(prevDebates => 
      prevDebates.map(debate => {
        if (debate.id === debateId) {
          const newDebate = { ...debate };
          if (side === VoteSide.FOR) {
            newDebate.votesFor += 1;
          } else {
            newDebate.votesAgainst += 1;
          }

          if (argumentId) {
            newDebate.arguments = newDebate.arguments.map(arg => {
              if (arg.id === argumentId) {
                return { ...arg, votes: arg.votes + 1 };
              }
              return arg;
            });
          }
          return newDebate;
        }
        return debate;
      })
    );
  }, []);

  const selectedDebate = debates.find(d => d.id === selectedDebateId) || null;

  return { debates, selectedDebate, selectDebate, castVote };
};
