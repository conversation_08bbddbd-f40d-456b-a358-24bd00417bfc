
export enum VoteSide {
  FOR = 'FOR',
  AGAINST = 'AGAINST',
}

export interface Reference {
  id: string;
  url: string;
  title: string;
}

export interface Argument {
  id: string;
  author: Participant;
  text: string;
  references: Reference[];
  side: VoteSide;
  votes: number;
}

export interface Participant {
  id: string;
  name: string;
  avatarUrl: string;
}

export interface Debate {
  id: string;
  title: string;
  description: string;
  claim: {
    author: Participant;
    text: string;
    references: Reference[];
  };
  participants: Participant[];
  proponent: Participant;
  opponent: Participant;
  arguments: Argument[];
  votesFor: number;
  votesAgainst: number;
  isLive: boolean;
}
