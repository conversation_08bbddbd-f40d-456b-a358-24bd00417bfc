
import React from 'react';
import { Argument, VoteSide } from '../types';
import { ParticipantIcon } from './ParticipantIcon';
import { VoteIcon, LinkIcon } from './icons';

interface ArgumentCardProps {
  argument: Argument;
  onVote: () => void;
}

export const ArgumentCard: React.FC<ArgumentCardProps> = ({ argument, onVote }) => {
  const isFor = argument.side === VoteSide.FOR;
  const borderColor = isFor ? 'border-green-500/50' : 'border-red-500/50';

  return (
    <div className={`bg-brand-surface rounded-lg p-4 border-l-4 ${borderColor} shadow-md`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <ParticipantIcon participant={argument.author} size="sm" />
          <span className="font-semibold text-brand-text">{argument.author.name}</span>
        </div>
        <div className="flex items-center gap-4">
          <span className={`font-bold text-sm ${isFor ? 'text-green-400' : 'text-red-400'}`}>
            {isFor ? 'FOR' : 'AGAINST'}
          </span>
          <button 
            onClick={onVote}
            className="flex items-center gap-2 text-brand-text-light hover:text-white transition-colors group"
          >
            <VoteIcon className="w-5 h-5 group-hover:text-brand-primary" />
            <span className="font-mono text-sm">{argument.votes}</span>
          </button>
        </div>
      </div>
      <p className="text-brand-text-light mb-4">{argument.text}</p>
      {argument.references.length > 0 && (
        <div>
          <h4 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2">References</h4>
          <ul className="space-y-1">
            {argument.references.map(ref => (
              <li key={ref.id}>
                <a href={ref.url} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 text-brand-primary/80 hover:text-brand-primary text-sm transition-colors">
                  <LinkIcon className="w-4 h-4" />
                  <span>{ref.title}</span>
                </a>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
