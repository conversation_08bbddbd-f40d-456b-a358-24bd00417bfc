
import React, { useState, useEffect, useCallback } from 'react';
import { Participant } from '../types';
import { MicrophoneIcon, MutedMicrophoneIcon, ZapIcon } from './icons';
import { ParticipantIcon } from './ParticipantIcon';

interface LiveDebateStageProps {
  proponent: Participant;
  opponent: Participant;
}

type PermissionStatus = 'idle' | 'requesting' | 'granted' | 'denied';

const SpeakerCard: React.FC<{
  participant: Participant;
  isSpeaking: boolean;
  isMuted: boolean;
  isClash: boolean;
  onToggleSpeak: () => void;
}> = ({ participant, isSpeaking, isMuted, isClash, onToggleSpeak }) => {
  
  const baseRing = 'ring-4 transition-all duration-300';
  let ringColor = 'ring-brand-surface-light';
  if(isClash) ringColor = 'ring-warning';
  else if(isSpeaking) ringColor = 'ring-success';
  else if(isMuted) ringColor = 'ring-danger';

  let statusText = 'Ready to Speak';
  let statusColor = 'text-brand-text-light';
  if(isClash) { statusText = 'CLASH!'; statusColor = 'text-warning'; }
  else if(isSpeaking) { statusText = 'Speaking'; statusColor = 'text-success'; }
  else if(isMuted) { statusText = 'Muted'; statusColor = 'text-danger'; }

  return (
    <div className="flex flex-col items-center gap-4 p-6 bg-brand-surface rounded-xl w-64">
      <div className={`${baseRing} ${ringColor} rounded-full`}>
        <ParticipantIcon participant={participant} size="lg" />
      </div>
      <h3 className="font-bold text-lg">{participant.name}</h3>
      <p className={`font-mono text-sm font-semibold ${statusColor}`}>{statusText}</p>
      <button 
        onClick={onToggleSpeak}
        className={`w-full flex items-center justify-center gap-2 px-4 py-3 rounded-lg font-semibold transition-all duration-200
        ${isSpeaking ? 'bg-success text-white shadow-lg' : 'bg-brand-surface-light hover:bg-brand-primary text-brand-text'}`}
      >
        {isSpeaking ? <MutedMicrophoneIcon className="w-6 h-6"/> : <MicrophoneIcon className="w-6 h-6"/>}
        <span>{isSpeaking ? 'Mute' : 'Speak'}</span>
      </button>
    </div>
  );
}

export const LiveDebateStage: React.FC<LiveDebateStageProps> = ({ proponent, opponent }) => {
  const [permission, setPermission] = useState<PermissionStatus>('idle');
  const [proponentState, setProponentState] = useState({ isSpeaking: false, isMuted: false });
  const [opponentState, setOpponentState] = useState({ isSpeaking: false, isMuted: false });
  const [isClash, setIsClash] = useState(false);

  const requestMicPermission = useCallback(async () => {
    setPermission('requesting');
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      // We don't need to use the stream, just confirm permission was granted.
      // In a real app, you'd attach this to an audio element or WebRTC peer.
      stream.getTracks().forEach(track => track.stop());
      setPermission('granted');
    } catch (err) {
      console.error("Microphone permission denied:", err);
      setPermission('denied');
    }
  }, []);

  useEffect(() => {
    const pSpeaking = proponentState.isSpeaking;
    const oSpeaking = opponentState.isSpeaking;

    if (pSpeaking && oSpeaking) {
      setIsClash(true);
      setProponentState(s => ({ ...s, isMuted: true }));
      setOpponentState(s => ({ ...s, isMuted: true }));
    } else if (pSpeaking) {
      setIsClash(false);
      setProponentState(s => ({ ...s, isMuted: false }));
      setOpponentState(s => ({ ...s, isMuted: true }));
    } else if (oSpeaking) {
      setIsClash(false);
      setProponentState(s => ({ ...s, isMuted: true }));
      setOpponentState(s => ({ ...s, isMuted: false }));
    } else {
      setIsClash(false);
      setProponentState(s => ({ ...s, isMuted: false }));
      setOpponentState(s => ({ ...s, isMuted: false }));
    }
  }, [proponentState.isSpeaking, opponentState.isSpeaking]);

  if (permission === 'denied') {
    return (
      <div className="bg-brand-surface rounded-lg p-8 text-center border-2 border-dashed border-danger">
        <h2 className="text-xl font-bold text-danger mb-2">Microphone Access Denied</h2>
        <p className="text-brand-text-light">Please enable microphone access in your browser settings to participate in the live debate.</p>
      </div>
    );
  }

  if (permission !== 'granted') {
    return (
      <div className="bg-brand-surface rounded-lg p-8 text-center border-2 border-dashed border-brand-surface-light">
        <h2 className="text-xl font-bold mb-4">Join the Live Stage</h2>
        <p className="text-brand-text-light mb-6">This feature requires microphone access to ensure fair speaking turns.</p>
        <button
          onClick={requestMicPermission}
          disabled={permission === 'requesting'}
          className="bg-brand-primary hover:bg-brand-primary-hover disabled:bg-gray-500 text-white font-bold py-3 px-6 rounded-lg transition-colors"
        >
          {permission === 'requesting' ? 'Requesting...' : 'Allow Microphone'}
        </button>
      </div>
    );
  }

  return (
    <div className="bg-brand-surface rounded-lg p-8 border-2 border-brand-primary/50">
      <h2 className="text-2xl font-bold text-center mb-2">Live Debate Stage</h2>
      <p className="text-brand-text-light text-center mb-8">Only one person can speak at a time. If both speak, both are muted.</p>
      <div className="flex items-start justify-center gap-8 flex-wrap relative">
        <SpeakerCard 
          participant={proponent} 
          isSpeaking={proponentState.isSpeaking}
          isMuted={proponentState.isMuted}
          isClash={isClash}
          onToggleSpeak={() => setProponentState(s => ({...s, isSpeaking: !s.isSpeaking}))}
        />
        <div className={`text-warning transition-opacity duration-300 ${isClash ? 'opacity-100' : 'opacity-0'}`}>
          <ZapIcon className="w-12 h-12 self-center mt-24"/>
        </div>
        <SpeakerCard
          participant={opponent}
          isSpeaking={opponentState.isSpeaking}
          isMuted={opponentState.isMuted}
          isClash={isClash}
          onToggleSpeak={() => setOpponentState(s => ({...s, isSpeaking: !s.isSpeaking}))}
        />
      </div>
    </div>
  );
};
