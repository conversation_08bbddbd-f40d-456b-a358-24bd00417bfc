
import React from 'react';
import { useDebateState } from './hooks/useDebateState';
import { DebateCard } from './components/DebateCard';
import { DebateView } from './components/DebateView';
import { VoteSide } from './types';

const App: React.FC = () => {
  const { debates, selectedDebate, selectDebate, castVote } = useDebateState();

  const handleVote = (side: VoteSide, argumentId?: string) => {
    if (selectedDebate) {
      castVote(selectedDebate.id, side, argumentId);
    }
  };

  return (
    <div className="min-h-screen bg-brand-bg font-sans">
      <header className="bg-brand-surface/50 backdrop-blur-lg border-b border-brand-surface-light sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <h1 className="text-2xl font-bold text-white tracking-tight">
              <span className="text-brand-primary">Agora:</span> The Debate Platform
            </h1>
          </div>
        </div>
      </header>

      <main>
        {selectedDebate ? (
          <DebateView 
            debate={selectedDebate} 
            onBack={() => selectDebate(null)}
            onVote={handleVote}
          />
        ) : (
          <div className="max-w-4xl mx-auto p-4 md:p-8">
            <h2 className="text-3xl font-extrabold text-white mb-6">Open Debates</h2>
            <div className="space-y-6">
              {debates.map((debate) => (
                <DebateCard key={debate.id} debate={debate} onSelect={selectDebate} />
              ))}
            </div>
          </div>
        )}
      </main>

      <footer className="bg-brand-surface mt-12 py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-brand-text-light text-sm">
          <p>&copy; {new Date().getFullYear()} Agora Platform. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default App;
